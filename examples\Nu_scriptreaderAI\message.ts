import { Timestamp } from "firebase-admin/firestore";
import { db } from "components/firebase";
import {
  collection,
  addDoc,
  serverTimestamp,
  onSnapshot,
  query,
  orderBy,
} from "firebase/firestore";

// Enhanced interface with all required fields
interface Message {
  id?: string;
  createdAt: Timestamp;
  text: string;
  userId: string;
  role: "user" | "assistant";
  fileDocumentId: string; // Required - Document ID for associated file
  category?: string;      // Message category/classification
  fileName?: string;      // Name of the associated file
  audioUrl?: string;      // URL to audio content (for assistant messages)
}

/**
 * Adds a message to a specific chat for a user with complete metadata
 * @param userId - The unique identifier of the user
 * @param chatId - The unique identifier of the chat
 * @param text - The message content
 * @param role - Whether the message is from the user or assistant
 * @param fileDocumentId - The document ID of the associated file (REQUIRED for data integrity)
 * @param category - The category/classification of the message (optional)
 * @param fileName - The name of the associated file (optional)
 * @param additionalFields - Any additional fields to include in the message document
 * @returns Promise containing the created document ID
 * @throws Error if userId, chatId, or fileDocumentId is empty or if database operation fails
 */
const addMessage = async (
  userId: string,
  chatId: string,
  text: string,
  role: "user" | "assistant",
  fileDocumentId: string, // Changed to required parameter
  additionalFields: Record<string, any> = {} // For extensibility
): Promise<string> => {
  try {
    // Validate inputs to prevent Firestore path errors
    if (!userId || userId.trim() === "") {
      throw new Error("userId is required and cannot be empty");
    }
    if (!chatId || chatId.trim() === "") {
      throw new Error("chatId is required and cannot be empty");
    }
    if (!fileDocumentId || fileDocumentId.trim() === "") {
      throw new Error("fileDocumentId is required and cannot be empty");
    }
    
    const messagesRef = collection(db, `users/${userId}/chats/${chatId}/messages`);
    
    // Prepare the message object with all required fields
    const message: Omit<Message, "id" | "createdAt"> = {
      text,
      userId,
      role,
      fileDocumentId, // Always include fileDocumentId
      ...additionalFields // Merge any additional fields (like audioUrl)
    };
    
    const docRef = await addDoc(messagesRef, {
      ...message,
      createdAt: serverTimestamp(),
    });
    
    //console.log(`Message added with ID: ${docRef.id} (fileDocumentId: ${fileDocumentId})`);
    return docRef.id;
  } catch (error) {
    console.error("Error adding message:", error);
    throw new Error(`Failed to add message: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
};

/**
 * Sets up a real-time listener for messages in a chat
 * @param userId - The unique identifier of the user
 * @param chatId - The unique identifier of the chat
 * @param callback - Function to call with updated messages
 * @returns Unsubscribe function to stop listening for updates
 */
const fetchMessages = (
  userId: string,
  chatId: string,
  callback: (messages: Message[]) => void
): (() => void) => {
  // Input validation to prevent Firestore path errors
  if (!userId || userId.trim() === "") {
    console.error("Error: userId is required and cannot be empty");
    callback([]); // Return empty array on error
    return () => {}; // Return no-op unsubscribe function
  }
  
  if (!chatId || chatId.trim() === "") {
    console.error("Error: chatId is required and cannot be empty");
    callback([]); // Return empty array on error
    return () => {}; // Return no-op unsubscribe function
  }

  const messagesRef = collection(db, `users/${userId}/chats/${chatId}/messages`);
  const q = query(messagesRef, orderBy("createdAt", "asc"));

  const unsubscribe = onSnapshot(
    q,
    (snapshot) => {
      const messages: Message[] = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Message[];
      //console.log(`Fetched ${messages.length} messages for chat ${chatId}`);
      callback(messages);
    },
    (error) => {
      console.error("Error fetching messages:", error);
      callback([]); // Provide empty array on error to avoid breaking the UI
    }
  );

  return unsubscribe;
};

export { addMessage, fetchMessages };