import React from 'react'
import ReactMarkdown from 'react-markdown'
import rehypeRaw from 'rehype-raw'
import { ExternalLink, Copy, Check } from 'lucide-react'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'

interface EnhancedMarkdownProps {
  content: string
  onItemClick?: (text: string) => void
  customTheme?: any
  onCopyCode?: (code: string) => void
  baseUrl?: string
}

export default function Component({
  content,
  onItemClick,
  customTheme,
  onCopyCode,
  baseUrl = typeof window !== 'undefined' ? window.location.origin : '',
}: EnhancedMarkdownProps) {
  const [copiedCodeBlock, setCopiedCodeBlock] = React.useState<string | null>(null)

  const isExternalUrl = (url?: string): boolean => {
    if (!url) return false
    try {
      if (url.startsWith('/')) return false
      if (!url.includes('://')) return false
      const urlObj = new URL(url)
      return urlObj.origin !== baseUrl
    } catch {
      return false
    }
  }

  const handleCopyCodeBlock = async (codeContent: string) => {
    try {
      await navigator.clipboard.writeText(codeContent)
      setCopiedCodeBlock(codeContent)
      onCopyCode?.(codeContent)
      setTimeout(() => setCopiedCodeBlock(null), 2000)
    } catch (err) {
      console.error('Failed to copy code block:', err)
    }
  }

  const handleListItemClick = (text: string) => {
    if (onItemClick) {
      const prefixedText = `Provide additional information on the following : ${text.trim()}`
      onItemClick(prefixedText)
    }
  }

  const markdownComponents = {
    code: ({ inline, className, children, ...props }: any) => {
      const match = /language-(\w+)/.exec(className || '')
      const language = match ? match[1] : 'typescript'

      return !inline ? (
        <div className="relative group">
          <div className="absolute right-2 top-2 flex gap-2">
            <button
              onClick={() => handleCopyCodeBlock(String(children))}
              className="hidden group-hover:flex items-center gap-2 px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded text-xs text-gray-300 hover:text-white transition-colors duration-200"
              aria-label={copiedCodeBlock === String(children) ? "Copied!" : "Copy code"}
            >
              {copiedCodeBlock === String(children) ? (
                <>
                  <Check className="w-3 h-3" />
                  <span>Copied!</span>
                </>
              ) : (
                <>
                  <Copy className="w-3 h-3" />
                  <span>Copy</span>
                </>
              )}
            </button>
          </div>
          <SyntaxHighlighter
            language={language}
            style={customTheme}
            className="rounded-lg !bg-gray-800 !p-4"
            showLineNumbers={false}
            wrapLines={true}
            customStyle={{
              margin: 0,
              background: 'none',
              boxShadow: 'none',
            }}
          >
            {String(children).trim()}
          </SyntaxHighlighter>
        </div>
      ) : (
        <code className="bg-gray-800 px-1 rounded" {...props}>
          {children}
        </code>
      )
    },

    li: ({ children, ...props }: { children?: React.ReactNode }) => {
      const renderContent = (children: React.ReactNode): React.ReactNode => {
        if (typeof children === 'string') {
          // Check if the content starts with an URL
          const urlRegex = /^(https?:\/\/[^\s]+)/;
          const match = children.match(urlRegex);

          if (match) {
            const url = match[1];
            const remainingText = children.slice(url.length);
            return (
              <>
                <a
                  href={url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 text-emerald-400 hover:text-emerald-300 group relative"
                >
                  {url}
                  <ExternalLink
                    className="w-3 h-3 opacity-70 group-hover:opacity-100 transition-opacity"
                    aria-label="Opens in new tab"
                  />
                </a>
                <span className="text-blue-300 group-hover:text-amber-300 transition-colors duration-200">
                  {remainingText}
                </span>
              </>
            );
          }

          const colonIndex = children.indexOf(':');
          if (colonIndex !== -1) {
            return (
              <>
                <span className="text-amber-500">
                  {children.slice(0, colonIndex + 1)}
                </span>
                <span className="text-blue-300 group-hover:text-amber-300 transition-colors duration-200">
                  {children.slice(colonIndex + 1)}
                </span>
              </>
            );
          }
          return children;
        }

        if (Array.isArray(children)) {
          return children.map((child, index) => (
            <React.Fragment key={index}>
              {renderContent(child)}
            </React.Fragment>
          ));
        }

        if (React.isValidElement(children) && children.props?.children) {
          if (children.type === 'a') {
            // If it's already a link component, enhance it with external link features
            const href = children.props.href;
            const isExternal = isExternalUrl(href);

            return React.cloneElement(
              children,
              {
                ...children.props,
                target: isExternal ? "_blank" : undefined,
                rel: isExternal ? "noopener noreferrer" : undefined,
                className: `inline-flex items-center gap-1 ${
                  isExternal ? "text-emerald-400 hover:text-emerald-300" : "text-blue-400 hover:text-blue-300"
                } group relative`
              },
              <>
                {children.props.children}
                {isExternal && (
                  <ExternalLink
                    className="w-3 h-3 opacity-70 group-hover:opacity-100 transition-opacity"
                    aria-label="Opens in new tab"
                  />
                )}
              </>
            );
          }
          return React.cloneElement(
            children,
            {},
            renderContent(children.props.children)
          );
        }

        return children;
      };

      const extractText = (child: React.ReactNode): string => {
        if (typeof child === 'string') return child;
        if (React.isValidElement(child) && child.props?.children) {
          return React.Children.toArray(child.props.children)
            .map(grandChild => extractText(grandChild))
            .join('');
        }
        return '';
      };

      // Recursive function to check for nested lists at any depth
      const checkForNestedLists = (node: React.ReactNode): boolean => {
        if (!node) return false;

        if (React.isValidElement(node)) {
          // Check if current element is a ul
          if (node.type === 'ul') return true;

          // Check children recursively
          if (node.props?.children) {
            const children = React.Children.toArray(node.props.children);
            return children.some(child => checkForNestedLists(child));
          }
        }

        if (Array.isArray(node)) {
          return node.some(child => checkForNestedLists(child));
        }

        return false;
      };

      const childrenArray = React.Children.toArray(children);
      const hasNestedList = checkForNestedLists(childrenArray);

      interface ReduceResult {
        content: React.ReactNode[];
        nestedList: React.ReactNode | null;
      }

      const { content, nestedList } = childrenArray.reduce<ReduceResult>(
        (acc, child) => {
          if (React.isValidElement(child) && child.type === 'ul') {
            return { ...acc, nestedList: child };
          }
          return { ...acc, content: [...acc.content, child] };
        },
        { content: [], nestedList: null }
      );

      const text = content
        .map(child => extractText(child))
        .join('');

      if (hasNestedList) {
        // Parent items - no highlighting, amber text
        return (
          <li>
            <div className="flex items-start gap-2">
              <span className="text-blue-300">•</span>
              <span className="flex-1 text-amber-500">
                {content}
              </span>
            </div>
            {nestedList}
          </li>
        );
      }

      // Individual items with no nested lists - highlightable
      return (
        <li>
          <div
            className="flex items-start gap-2 group cursor-pointer hover:bg-gray-800 p-1 rounded transition-colors"
            onClick={() => handleListItemClick(text)}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleListItemClick(text);
              }
            }}
            {...props}
          >
            <span className="text-blue-300 group-hover:text-blue-300">•</span>
            <span className="flex-1 text-blue-300 group-hover:text-amber-300 transition-colors duration-200">
              {renderContent(content)}
            </span>
          </div>
        </li>
      );
    },

    a: ({ href, children }: { href?: string; children?: React.ReactNode }) => {
      const external = isExternalUrl(href)

      return (
        <a
          href={href}
          target={external ? "_blank" : undefined}
          rel={external ? "noopener noreferrer" : undefined}
          className={`
            inline-flex items-center gap-1
            ${external
              ? "text-emerald-400 hover:text-emerald-300"
              : "text-blue-400 hover:text-blue-300"
            }
            group relative
          `}
        >
          <span>{children}</span>
          {external && (
            <>
              <ExternalLink
                className="w-3 h-3 opacity-70 group-hover:opacity-100 transition-opacity"
                aria-label="Opens in new tab"
              />
              <span className="sr-only">Opens in new tab</span>
            </>
          )}
        </a>
      )
    },

    p: ({ children }: { children?: React.ReactNode }) => {
      // Check if this paragraph contains script dialogue (has a character name with colon)
      const childrenStr = String(React.isValidElement(children) ?
        React.Children.toArray(children.props?.children).join('') : children || '');

      // Check for dialogue by looking for character name with colon or line-number span
      const hasCharacterName = (childrenStr.includes('**') && childrenStr.includes(':')) ||
                              childrenStr.includes('line-number');

      return (
        <p className={`mb-4 last:mb-0 leading-relaxed ${hasCharacterName ? 'script-dialogue' : ''}`}>
          {children}
        </p>
      );
    },

    ul: ({ children }: { children?: React.ReactNode }) => (
      <ul className="space-y-2 mb-4" role="list">{children}</ul>
    ),

    ol: ({ children }: { children?: React.ReactNode }) => (
      <ol className="list-decimal pl-6 mb-4 space-y-2" role="list">{children}</ol>
    ),

    blockquote: ({ children }: { children?: React.ReactNode }) => (
      <blockquote className="border-l-4 border-gray-500 pl-4 py-1 my-4 italic">
        {children}
      </blockquote>
    ),

    strong: ({ children }: { children?: React.ReactNode }) => {
      // Check if this is a character name in a script line
      const childrenStr = String(children || '');

      // Check if this is a character name (likely ends with a colon)
      const isCharacterName = childrenStr.includes(':');

      // Apply styling based on content type
      let className = 'font-bold ';
      if (isCharacterName) {
        className += 'text-amber-500'; // Character names
      } else {
        className += 'text-amber-400'; // Other bold text
      }

      return (
        <strong className={className}>
          {children}
        </strong>
      );
    },
  }

  return (
    <div className="prose prose-invert max-w-none">
      <ReactMarkdown rehypePlugins={[rehypeRaw]} components={markdownComponents}>
        {content}
      </ReactMarkdown>
    </div>
  )
}