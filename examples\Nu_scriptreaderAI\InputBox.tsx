// EnhancedInputBox.tsx
import { useState, useRef, useEffect } from "react";
import { Send, Paperclip, Mic } from "lucide-react";

interface InputBoxProps {
  onSendMessage: (content: string) => void;
  isLoading?: boolean;
  onAttachFile?: () => void;
  onMicClick?: () => void;
  isRecording?: boolean;
  placeholder?: string;
  disabled?: boolean;
}

export function EnhancedInputBox({
  onSendMessage,
  isLoading = false,
  onAttachFile,
  onMicClick,
  isRecording = false,
  placeholder = "Ask SceneMate",
  disabled = false,
}: InputBoxProps) {
  const [inputValue, setInputValue] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea based on content
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "60px"; // Reset height
      const scrollHeight = textarea.scrollHeight;
      textarea.style.height = `${Math.max(60, Math.min(200, scrollHeight))}px`;
    }
  }, [inputValue]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || isLoading || disabled) return;

    onSendMessage(inputValue);
    setInputValue(""); // Clear input after sending
  };

  return (
    <div className="w-full max-w-full px-2 sm:px-2 md:px-2 py-1 backdrop-blur-sm border-t border-gray-200 dark:border-gray-800 pt-2 transition-colors duration-300">
      <form onSubmit={handleSubmit} className="max-w-5xl mx-auto">
        <div className="relative flex items-end gap-2 bg-white dark:bg-black/40 rounded-2xl border border-gray-200 dark:border-gray-700 focus-within:border-blue-500 dark:focus-within:border-purple-500 transition-all shadow-lg">
          <div className="flex-1 min-w-0 relative">
            <textarea
              ref={textareaRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder={placeholder}
              className="w-full resize-none bg-transparent rounded-tl-2xl rounded-bl-2xl p-4 pr-2 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none min-h-[60px] max-h-[200px] overflow-y-auto transition-colors duration-300"
              disabled={isLoading || disabled}
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  handleSubmit(e);
                }
              }}
              rows={1}
            />
          </div>
          <div className="flex items-center p-2">
            {onAttachFile && (
              <button
                type="button"
                onClick={onAttachFile}
                disabled={isLoading || disabled}
                className={`p-2 rounded-full text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-purple-400 hover:bg-blue-50 dark:hover:bg-purple-900/20 transition-colors mr-1 ${
                  (isLoading || disabled) ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                <Paperclip className="w-5 h-5" />
              </button>
            )}
            {onMicClick && (
              <button
                type="button"
                onClick={onMicClick}
                disabled={isLoading || disabled}
                className={`p-2 rounded-full transition-colors mr-1 ${
                  isRecording
                    ? "bg-red-500 text-white"
                    : "text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-purple-400 hover:bg-blue-50 dark:hover:bg-purple-900/20"
                } ${(isLoading || disabled) ? "opacity-50 cursor-not-allowed" : ""}`}
              >
                <Mic className="w-5 h-5" />
              </button>
            )}
            <button
              type="submit"
              disabled={isLoading || !inputValue.trim() || disabled}
              className={`p-3 rounded-full transition-all ${
                isLoading || !inputValue.trim() || disabled
                  ? "bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                  : "bg-blue-600 dark:bg-purple-600 text-white hover:bg-blue-700 dark:hover:bg-purple-700 hover:shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-purple-500/20"
              }`}
            >
              <Send className="w-5 h-5" />
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}