// --- File: components/ScriptReader/ScriptMarkdownContent.tsx ---

import React from 'react'
import ReactMarkdown from 'react-markdown'
import rehypeRaw from 'rehype-raw'
import { ExternalLink, Copy, Check } from 'lucide-react'
import { <PERSON>rism as Syntax<PERSON><PERSON>lighter } from 'react-syntax-highlighter' // Keep if code blocks are possible

interface ScriptMarkdownProps {
  content: string
  onItemClick?: (text: string) => void // Keep if list item clicks are needed elsewhere
  customTheme?: any // Keep for syntax highlighting
  onCopyCode?: (code: string) => void // Keep for code blocks
  baseUrl?: string
}

export default function ScriptMarkdownContent({
  content,
  onItemClick, // Pass down if needed by lists
  customTheme, // Pass down if needed by code blocks
  onCopyCode, // Pass down if needed by code blocks
  baseUrl = typeof window !== 'undefined' ? window.location.origin : '',
}: ScriptMarkdownProps) {
  const [copiedCodeBlock, setCopiedCodeBlock] = React.useState<string | null>(null)

  // --- Remove or simplify the preprocessing logic ---
  // This logic was designed for the old "Content:" format.
  // If the new formatting from markdownFormatterTool is reliable,
  // this preprocessing might not be needed or should be adapted.
  // For now, let's bypass it and directly use the content assuming
  // it's correctly formatted markdown from the tool.
  const processedContent = content; // Use content directly

  /* --- Original Preprocessing Logic (commented out or removed) ---
  const processedContent = React.useMemo(() => {
     // ... complex logic to parse "Content:" format ...
     return content; // Simplified return for now
  }, [content]);
  */

  // Helper functions for external links, code copying, and text extraction
  const isExternalUrl = (url?: string): boolean => {
    if (!url) return false;
    try {
      const urlObj = new URL(url, baseUrl);
      return urlObj.origin !== baseUrl;
    } catch {
      return false;
    }
  };

  const handleCopyCodeBlock = async (codeContent: string) => {
    try {
      await navigator.clipboard.writeText(codeContent);
      setCopiedCodeBlock(codeContent);
      setTimeout(() => setCopiedCodeBlock(null), 2000);
      if (onCopyCode) onCopyCode(codeContent);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  const handleListItemClick = (text: string) => {
    if (onItemClick) onItemClick(text);
  };

  const extractText = (child: React.ReactNode): string => {
    if (typeof child === 'string') return child;
    if (typeof child === 'number') return String(child);
    if (React.isValidElement(child) && child.props.children) {
      return extractText(child.props.children);
    }
    if (Array.isArray(child)) {
      return child.map(extractText).join('');
    }
    return '';
  };


  const markdownComponents: React.ComponentProps<typeof ReactMarkdown>['components'] = {
    // Code blocks with syntax highlighting and copy functionality
    code: ({ inline, className, children, ...props }: any) => {
      const match = /language-(\w+)/.exec(className || '');
      const language = match ? match[1] : '';
      const codeContent = String(children).replace(/\n$/, '');

      if (!inline && language) {
        return (
          <div className="relative group">
            <SyntaxHighlighter
              style={customTheme}
              language={language}
              PreTag="div"
              className="rounded-md text-sm"
            >
              {codeContent}
            </SyntaxHighlighter>
            <button
              onClick={() => handleCopyCodeBlock(codeContent)}
              className="absolute top-2 right-2 p-1 bg-gray-700 hover:bg-gray-600 rounded opacity-0 group-hover:opacity-100 transition-opacity"
              title="Copy code"
            >
              {copiedCodeBlock === codeContent ? (
                <Check className="w-4 h-4 text-green-400" />
              ) : (
                <Copy className="w-4 h-4 text-gray-300" />
              )}
            </button>
          </div>
        );
      }

      return (
        <code className={`inline-block bg-gray-200 text-black px-1 rounded ${className || ''}`} {...props}>
          {children}
        </code>
      );
    },

    // Paragraphs - Now centered for dialogue text
    p: ({ children }: { children?: React.ReactNode }) => {
      const childrenStr = String(children || '');

      // Check if this paragraph contains "Sounds" text
      if (childrenStr.includes('Sounds')) {
        return (
          <p className="mb-3 last:mb-0 leading-relaxed text-center inline-block bg-black text-lime-400 px-2 py-1">
            {children}
          </p>
        );
      }

      return (
        <p className="mb-3 last:mb-0 leading-relaxed text-black text-center">
          {children}
        </p>
      );
    },

    // Lists - Keep simple styling for metadata character lists etc.
    ul: ({ children }: { children?: React.ReactNode }) => (
      <ul className="list-disc pl-6 mb-4 space-y-1 text-black" role="list">{children}</ul>
    ),
    ol: ({ children }: { children?: React.ReactNode }) => (
      <ol className="list-decimal pl-6 mb-4 space-y-1 text-black" role="list">{children}</ol>
    ),
    // List items - Simplified, remove character parsing logic
    li: ({ children, ...props }: { children?: React.ReactNode }) => {
      // Basic list item, potentially clickable if onItemClick is used
      const text = extractText(children); // Extract text if click handler needs it
      return (
        <li
          onClick={onItemClick ? () => handleListItemClick(text) : undefined}
          className={`mb-1 ${onItemClick ? 'cursor-pointer hover:text-gray-700' : ''} text-black`}
          {...props}
        >
          {children}
        </li>
      );
    },

    // Links with external link indicator
    a: ({ href, children }: { href?: string; children?: React.ReactNode }) => {
      const isExternal = isExternalUrl(href);
      return (
        <a
          href={href}
          target={isExternal ? "_blank" : undefined}
          rel={isExternal ? "noopener noreferrer" : undefined}
          className="text-blue-600 hover:underline inline-flex items-center gap-1"
        >
          {children}
          {isExternal && <ExternalLink className="w-3 h-3" />}
        </a>
      );
    },

    // Blockquotes (keep if needed)
    blockquote: ({ children }: { children?: React.ReactNode }) => (
      <blockquote className="border-l-4 border-gray-300 pl-4 py-1 my-4 italic text-gray-700">
        {children}
      </blockquote>
    ),

    // Strong text - Simplified, just bold. Remove character name detection.
    strong: ({ children }: { children?: React.ReactNode }) => {
      return (
        <strong className="font-semibold text-black">
          {children}
        </strong>
      );
    },

    // Emphasized text (italics) - Keep styling for notes
    em: ({ children }: { children?: React.ReactNode }) => {
      // Style specifically for notes (often italicized in markdown)
      return (
        <em className="italic text-gray-600">
          {children}
        </em>
      );
    },

    // Headings
    h1: ({ children }: { children?: React.ReactNode }) => (
      <h1 className="text-2xl font-bold mb-6 text-black border-b pb-2">{children}</h1>
    ),
    // H2 - Differentiate between Metadata Headers and Character Names
    h2: ({ children }: { children?: React.ReactNode }) => {
      const childrenStr = String(children || '');
      // Assume headers like "Script", "Summary", "Characters" are metadata headers
      const isMetadataHeader = ['Script', 'Summary', 'Characters'].includes(childrenStr);

      if (!isMetadataHeader) {
           // Apply character name styling (using the existing class or direct styles)
          return (
              // Use the dedicated class for styling consistency
              <div className="character-dialogue">
                  {/* Render the character name */}
                  <strong>{children}</strong>
              </div>
          );
      }

      // Default H2 styling for section headers like "Script", "Summary"
      return (
          <h2 className="text-xl font-semibold mb-4 mt-6 text-black border-b border-gray-200 pb-2">{children}</h2>
      );
    },
    // H3 - For metadata like "By Author"
    h3: ({ children }: { children?: React.ReactNode }) => (
      <h3 className="text-lg font-medium mb-4 text-gray-700">{children}</h3>
    ),
  }

  return (
    // Ensure the container provides a white background as per the target image
    <div className="prose max-w-none bg-white p-6 rounded-lg shadow-md mb-4 mx-auto script-content-container">

      <ReactMarkdown
          rehypePlugins={[rehypeRaw]} // Allows processing HTML within markdown if needed (like fallback)
          components={markdownComponents}
      >
          {processedContent}
      </ReactMarkdown>

      {/* Define Styles matching the target image */}
      <style jsx global>{`
        /* Script content container - Adjust max-width as needed */
        .script-content-container {
          max-width: 700px; /* Restore wider width */
          margin-left: auto;
          margin-right: auto;
          font-family: "Courier New", Courier, monospace; /* Typewriter font */
          color: #333; /* Default text color */
          padding-top: 1rem; /* Add some top padding */
        }

        /* First character name in the script */
        .script-content-container h2:first-of-type {
          margin-top: 1rem; /* Less margin for first character */
        }

        /* Character name styling */
        .character-dialogue {
          text-align: center;
          margin-top: 3rem; /* Increased space above character name */
          margin-bottom: 0.2rem; /* Reduced space below character name */
          font-size: 1.25rem; /* Larger size */
          font-weight: 700; /* Make it bolder */
          color: #000; /* Darker color for better contrast */
          padding-bottom: 0; /* Remove padding */
          font-family: "Courier New", Courier, monospace; /* Typewriter font */
          /* No border to match the screenshot */
        }

        /* Ensure strong elements within character dialogue are properly styled */
        .character-dialogue strong {
          font-weight: 700; /* Bold font weight */
          display: block; /* Make it a block element for proper centering */
          text-align: center; /* Center alignment */
          color: #000; /* Ensure black color */
          font-family: "Courier New", Courier, monospace; /* Typewriter font */
          margin-bottom: 0.2rem; /* Reduced space below the character name */
        }

        /* Ensure paragraphs (dialogue) following character names have correct spacing */
        .character-dialogue + p {
          margin-top: 0.3rem; /* Minimal space - character name already has margin-bottom */
          padding-top: 0; /* Remove any padding */
          margin-bottom: 1.5rem; /* Space after dialogue paragraph for separation */
        }

        /* Styling for paragraphs (dialogue, notes) */
        .script-content-container p {
           line-height: 1.6; /* Comfortable line height for dialogue */
           color: #333; /* Ensure dialogue text color */
           text-align: center; /* Center all dialogue text */
           max-width: 85%; /* Limit width for better readability */
           margin-left: auto;
           margin-right: auto;
           margin-top: 0; /* Remove top margin - spacing handled by character names */
           margin-bottom: 1.0rem; /* Space after paragraphs for better separation */
        }

        /* Styling for italicized notes */
        .script-content-container em {
           color: #555; /* Slightly different color for notes */
           display: block; /* Make notes appear on their own line */
           margin-top: 0.25rem;
           margin-bottom: 1rem;
        }

        /* Styling for metadata lists (Characters) */
        .script-content-container ul {
          margin-top: 0.5rem;
          margin-bottom: 1.5rem;
          padding-left: 2rem; /* Indent list */
        }
        .script-content-container li {
          margin-bottom: 0.25rem;
        }

        /* Adjust heading styles if needed */
        .script-content-container h2 { /* Script, Summary Headers */
           margin-top: 2rem;
           margin-bottom: 1rem;
           padding-bottom: 0.25rem;
           border-bottom: 1px solid #ddd;
           color: #111;
        }
         .script-content-container h3 { /* By Author */
            margin-top: 0.5rem;
            margin-bottom: 1.5rem;
            font-style: italic;
            color: #444;
         }

        /* Remove styles for elements no longer used in this format */
        .line-number { display: none; }
        .script-dialogue strong { /* If .script-dialogue class was used before */
           /* Reset any specific styles if needed */
        }
      `}</style>
    </div>
  )
}