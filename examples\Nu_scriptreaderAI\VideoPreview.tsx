"use client";

import React, { useRef, useEffect, useState } from "react";
import { Video, VideoOff } from "lucide-react";

interface VideoPreviewProps {
  stream: MediaStream | null;
  isRecording: boolean;
  recordingDuration: number;
  className?: string;
}

function VideoPreview({ stream, isRecording, recordingDuration, className = "" }: VideoPreviewProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);

  useEffect(() => {
    if (videoRef.current && stream) {
      videoRef.current.srcObject = stream;
      videoRef.current.play().catch(console.error);
    }
  }, [stream]);

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const toggleVideo = () => {
    if (stream) {
      const videoTrack = stream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        setIsVideoEnabled(videoTrack.enabled);
      }
    }
  };

  return (
    <div className={`relative bg-black rounded-lg overflow-hidden ${className}`}>
      {stream ? (
        <>
          <video
            ref={videoRef}
            autoPlay
            muted
            playsInline
            className="w-full h-full object-cover"
          />
          
          {/* Recording indicator */}
          {isRecording && (
            <div className="absolute top-3 left-3 flex items-center space-x-2 bg-red-600/90 backdrop-blur-sm px-3 py-1 rounded-full">
              <div className="w-2 h-2 bg-red-300 rounded-full animate-pulse" />
              <span className="text-white text-sm font-medium">REC</span>
              <span className="text-red-100 text-sm">{formatDuration(recordingDuration)}</span>
            </div>
          )}

          {/* Video controls */}
          <div className="absolute bottom-3 right-3 flex items-center space-x-2">
            <button
              onClick={toggleVideo}
              className="p-2 bg-black/50 hover:bg-black/70 rounded-full text-white transition-colors"
              title={isVideoEnabled ? "Turn off camera" : "Turn on camera"}
            >
              {isVideoEnabled ? (
                <Video className="w-4 h-4" />
              ) : (
                <VideoOff className="w-4 h-4" />
              )}
            </button>
          </div>

          {/* Video disabled overlay */}
          {!isVideoEnabled && (
            <div className="absolute inset-0 bg-gray-900 flex items-center justify-center">
              <div className="text-center text-white">
                <VideoOff className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p className="text-sm opacity-75">Camera is off</p>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="w-full h-full flex items-center justify-center text-gray-400">
          <div className="text-center">
            <Video className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No camera stream</p>
          </div>
        </div>
      )}
    </div>
  );
}

export default VideoPreview;
