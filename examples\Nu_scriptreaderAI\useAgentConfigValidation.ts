import { useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { validateUserAgentConfiguration } from '../lib/userAgentManager';

interface UseAgentConfigValidationReturn {
  isValidating: boolean;
  validationResult: {
    success: boolean;
    agentId?: string;
    configurationFixed: boolean;
    error?: string;
    details?: string;
  } | null;
  validateConfiguration: () => Promise<void>;
  clearResult: () => void;
}

/**
 * Hook to validate and fix user agent configuration for proper WebSocket message flow
 * This ensures the send_text_for_tts_and_tools flag is enabled for text response transmission
 */
export function useAgentConfigValidation(): UseAgentConfigValidationReturn {
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<{
    success: boolean;
    agentId?: string;
    configurationFixed: boolean;
    error?: string;
    details?: string;
  } | null>(null);
  const { data: session } = useSession();

  const validateConfiguration = useCallback(async () => {
    if (!session?.user?.email) {
      setValidationResult({
        success: false,
        configurationFixed: false,
        error: 'User must be signed in to validate agent configuration'
      });
      return;
    }

    setIsValidating(true);
    setValidationResult(null);

    try {
      ////console.log(`[useAgentConfigValidation] Starting validation for ${session.user.email}`);
      
      const result = await validateUserAgentConfiguration(session.user.email);
      
      ////console.log(`[useAgentConfigValidation] Validation completed:`, result);
      setValidationResult(result);

      if (result.success && result.configurationFixed) {

      } else if (result.success && !result.configurationFixed) {
        //console.log(`[useAgentConfigValidation] ✅ Configuration was already correct for agent ${result.agentId}`);
      } else {
        console.error(`[useAgentConfigValidation] ❌ Validation failed:`, result.error);
      }

    } catch (error) {
      console.error(`[useAgentConfigValidation] Error during validation:`, error);
      setValidationResult({
        success: false,
        configurationFixed: false,
        error: error instanceof Error ? error.message : 'Unknown error during validation'
      });
    } finally {
      setIsValidating(false);
    }
  }, [session?.user?.email]);

  const clearResult = useCallback(() => {
    setValidationResult(null);
  }, []);

  return {
    isValidating,
    validationResult,
    validateConfiguration,
    clearResult
  };
}
