// RehearsalHistory.tsx
import { useState, useEffect } from "react";
import { XCircle, ChevronDown, ChevronUp } from "lucide-react";
import {
  getFirestore,
  collection,
  query,
  orderBy,
  getDocs,
  where,
  Timestamp,
} from "firebase/firestore";

interface ChatHistoryProps {
  isVisible: boolean;
  onClose: () => void;
  currentChatId: string | null;
  onChatSelect: (chatId: string) => void;
  userId: string;
  namespace: string | null;
}

interface ChatHistoryItem {
  id: string;
  firstMessage: string;
  createdAt: Timestamp | Date | string; // Updated to allow multiple types
  fileNamespace?: string;
}

type TimeFilter = "Today" | "Yesterday" | "This week" | "Last 7 days" | "All";

export function RehearsalHistory({
  isVisible,
  onClose,
  currentChatId,
  onChatSelect,
  userId,
  namespace,
}: ChatHistoryProps) {
  const [chats, setChats] = useState<ChatHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [timeFilter, setTimeFilter] = useState<TimeFilter>("Last 7 days");
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);

  useEffect(() => {
    if (isVisible && userId) {
      loadChatHistory();
    }
  }, [isVisible, userId, namespace, timeFilter]);

  const getTimeFilterDate = (filter: TimeFilter): Date => {
    const now = new Date();
    switch (filter) {
      case "Today":
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      case "Yesterday":
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        yesterday.setHours(0, 0, 0, 0);
        return yesterday;
      case "This week":
        const thisWeek = new Date();
        // Set to the first day of the week (Sunday)
        thisWeek.setDate(thisWeek.getDate() - thisWeek.getDay());
        thisWeek.setHours(0, 0, 0, 0);
        return thisWeek;
      case "Last 7 days":
        const lastWeek = new Date();
        lastWeek.setDate(lastWeek.getDate() - 7);
        return lastWeek;
      case "All":
      default:
        return new Date(0); // Beginning of time
    }
  };

  const loadChatHistory = async () => {
    if (!userId) return;

    setIsLoading(true);
    try {
      const chatsRef = collection(getFirestore(), `users/${userId}/chats`);
      let chatsQuery = query(chatsRef, orderBy("createdAt", "desc"));

      if (namespace) {
        chatsQuery = query(
          chatsRef,
          where("fileNamespace", "==", namespace),
          orderBy("createdAt", "desc")
        );
      }

      const chatsSnapshot = await getDocs(chatsQuery);
      const filterDate = getTimeFilterDate(timeFilter);

      let filteredChats = chatsSnapshot.docs
        .map((doc) => ({
          id: doc.id,
          firstMessage: doc.data().firstMessage || "New rehearsal",
          createdAt: doc.data().createdAt,
          fileNamespace: doc.data().fileNamespace,
        }))
        .filter((chat) => {
          if (timeFilter === "All") return true;

          let chatDate: Date | null = null;
          if (chat.createdAt instanceof Timestamp) {
            chatDate = chat.createdAt.toDate();
          } else if (chat.createdAt instanceof Date) {
            chatDate = chat.createdAt;
          } else if (typeof chat.createdAt === 'string') {
            chatDate = new Date(chat.createdAt); // Parse the ISO string
          } else {
            console.error("Invalid createdAt type:", chat.createdAt);
            return false;
          }

          // Handle invalid date parsing
          if (!chatDate || isNaN(chatDate.getTime())) {
             console.error("Invalid date after parsing:", chat.createdAt);
             return false;
          }

          if (timeFilter === "Yesterday") {
            if (!chatDate) return false;
            const yesterday = getTimeFilterDate("Yesterday");
            const tomorrow = new Date(yesterday);
            tomorrow.setDate(tomorrow.getDate() + 1);
            return chatDate >= yesterday && chatDate < tomorrow;
          }

          return chatDate >= filterDate;
        });

      setChats(filteredChats);
    } catch (error) {
      console.error("Error loading chat history:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const groupChatsByDate = () => {
    const groups: { [key: string]: ChatHistoryItem[] } = {};

    chats.forEach((chat) => {
      let chatDate: Date | null = null;
      if (chat.createdAt instanceof Timestamp) {
        chatDate = chat.createdAt.toDate();
      } else if (chat.createdAt instanceof Date) {
        chatDate = chat.createdAt;
      } else if (typeof chat.createdAt === 'string') {
          chatDate = new Date(chat.createdAt);
      } else {
        console.error("Invalid createdAt type:", chat.createdAt);
        return; // Skip this chat
      }

        // Handle potentially invalid date
        if (!chatDate || isNaN(chatDate.getTime())) {
            console.error("Invalid date after parsing:", chat.createdAt);
            return;
        }


      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      let dateKey: string;

      if (chatDate.toDateString() === today.toDateString()) {
        dateKey = "Today";
      } else if (chatDate.toDateString() === yesterday.toDateString()) {
        dateKey = "Yesterday";
      } else {
        dateKey = chatDate.toLocaleDateString();
      }

      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(chat);
    });

    return groups;
  };

  const chatGroups = groupChatsByDate();

    const formatTime = (timestamp: Timestamp | Date | string | undefined): string => {
        if (!timestamp) return "";

        let date: Date;
        if (timestamp instanceof Timestamp) {
            date = timestamp.toDate();
        } else if (timestamp instanceof Date) {
            date = timestamp;
        } else {
            date = new Date(timestamp); // Parse the ISO string
        }
        
        //check if date is valid
        if(isNaN(date.getTime())){
            console.error("Invalid date after parsing:", timestamp);
            return "";
        }
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

  const formatDate = (dateStr: string): string => {
    if (dateStr === "Today" || dateStr === "Yesterday") {
      return dateStr;
    }
    return dateStr;
  };

  const truncateMessage = (message: string, maxLength: number = 60): string => {
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + "...";
  };

  return (
    <div
      className={`fixed inset-y-0 left-0 w-80 bg-gray-900 z-20 transform ${
        isVisible ? "translate-x-0" : "-translate-x-full"
      } transition-transform duration-200 ease-in-out md:relative md:translate-x-0 ${
        isVisible ? "md:block" : "md:hidden"
      }`}
    >
      <div className="p-4 border-b border-white/10 flex justify-between items-center">
        <h2 className="text-xl font-bold text-white">Rehearsals</h2>
        <button
          onClick={onClose}
          className="p-1 rounded-full bg-transparent text-gray-400 hover:text-white"
        >
          <XCircle className="w-6 h-6" />
        </button>
      </div>

      <div className="p-4 border-b border-white/10">
        <div
          className="flex justify-between items-center cursor-pointer"
          onClick={() => setIsFilterOpen(!isFilterOpen)}
        >
          <span className="text-gray-300">Filter by time</span>
          {isFilterOpen ? (
            <ChevronUp className="w-5 h-5 text-gray-400" />
          ) : (
            <ChevronDown className="w-5 h-5 text-gray-400" />
          )}
        </div>

        {isFilterOpen && (
          <div className="mt-2 space-y-1">
            {["Today", "Yesterday", "This week", "Last 7 days", "All"].map(
              (filter) => (
                <div
                  key={filter}
                  className={`px-3 py-2 rounded-md cursor-pointer ${
                    timeFilter === filter
                      ? "bg-purple-600 text-white"
                      : "text-gray-300 hover:bg-gray-800"
                  }`}
                  onClick={() => {
                    setTimeFilter(filter as TimeFilter);
                    setIsFilterOpen(false);
                  }}
                >
                  {filter}
                </div>
              )
            )}
          </div>
        )}
      </div>

      <div className="overflow-y-auto h-full pb-20">
        {isLoading ? (
          <div className="p-4 text-gray-400 text-center">Loading...</div>
        ) : (
          <div>
            {Object.keys(chatGroups).length === 0 ? (
              <div className="p-4 text-gray-400 text-center">
                No rehearsals found
              </div>
            ) : (
              Object.entries(chatGroups).map(([date, dateChats]) => (
                <div key={date} className="mb-4">
                  <div className="px-4 py-2 text-sm font-semibold text-gray-400 bg-gray-800/30">
                    {formatDate(date)}
                  </div>
                  <div>
                    {dateChats.map((chat) => (
                      <div
                        key={chat.id}
                        className={`p-4 cursor-pointer border-l-4 ${
                          chat.id === currentChatId
                            ? "border-purple-500 bg-gray-800/50"
                            : "border-transparent hover:bg-gray-800/30"
                        }`}
                        onClick={() => onChatSelect(chat.id)}
                      >
                        <div className="text-white font-medium truncate">
                          {truncateMessage(chat.firstMessage)}
                        </div>
                        <div className="flex justify-between items-center mt-1">
                          <span className="text-xs text-gray-400">
                            {formatTime(chat.createdAt)}
                          </span>
                          {chat.fileNamespace && (
                            <span className="text-xs bg-gray-700 rounded-full px-2 py-0.5 text-gray-300">
                              {truncateMessage(chat.fileNamespace, 15)}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
}