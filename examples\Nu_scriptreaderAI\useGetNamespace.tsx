import { useState, useEffect } from 'react';
import { getFirestore, doc, getDoc, collection, query, where, getDocs } from 'firebase/firestore';

/**
 * Hook to retrieve namespace information by file ID
 */
export const useGetNamespace = (userId: string, fileId: string | null, category: string = 'SceneMate') => {
  const [namespace, setNamespace] = useState<string | null>(null);
  const [fileName, setFileName] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchNamespace = async () => {
      if (!userId || !fileId) {
        setLoading(false);
        return;
      }

      try {
        const db = getFirestore();
        const fileDocRef = doc(db, `users/${userId}/files/${fileId}`);
        const fileDocSnap = await getDoc(fileDocRef);

        if (fileDocSnap.exists()) {
          const fileData = fileDocSnap.data();
          if (fileData.category === category) {
            setNamespace(fileData.namespace);
            setFileName(fileData.name);
          } else {
            setError('Category mismatch');
          }
        } else {
          setError('File not found');
        }
      } catch (err) {
        setError(`Error fetching namespace: ${err instanceof Error ? err.message : 'Unknown error'}`);
      } finally {
        setLoading(false);
      }
    };

    fetchNamespace();
  }, [userId, fileId, category]);

  return { namespace, fileName, loading, error };
};

/**
 * Hook to retrieve namespace information by file name
 */
export const useGetNamespaceByName = (userId: string, fileName: string | null, category: string = 'SceneMate') => {
  const [namespace, setNamespace] = useState<string | null>(null);
  const [fileId, setFileId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchNamespace = async () => {
      if (!userId || !fileName) {
        setLoading(false);
        return;
      }

      try {
        const db = getFirestore();
        const filesRef = collection(db, `users/${userId}/files`);
        const q = query(filesRef, where('name', '==', fileName), where('category', '==', category));
        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          const fileData = querySnapshot.docs[0].data();
          setNamespace(fileData.namespace);
          setFileId(querySnapshot.docs[0].id);
        } else {
          setError('File not found');
        }
      } catch (err) {
        setError(`Error fetching namespace: ${err instanceof Error ? err.message : 'Unknown error'}`);
      } finally {
        setLoading(false);
      }
    };

    fetchNamespace();
  }, [userId, fileName, category]);

  return { namespace, fileId, loading, error };
};