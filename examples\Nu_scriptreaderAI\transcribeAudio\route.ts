// app/api/transcribeAudio/route.ts

import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check for required environment variables
    if (!process.env.OPENAI_API_KEY) {
      console.error('[Transcription API] Missing OPENAI_API_KEY environment variable');
      return NextResponse.json({
        success: false,
        error: "Service configuration error",
        details: "OpenAI API key not configured"
      }, { status: 500 });
    }

    // Dynamic import of OpenAI to prevent build-time issues
    const OpenAI = (await import("openai")).default;
    const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

    // 1. Enhanced session retrieval with detailed debugging
    console.log("[Transcription API] Request received");

    const session = await getServerSession(authOptions);
    console.log("[Transcription API] Session state:", {
      exists: !!session,
      hasUser: !!session?.user,
      email: session?.user?.email || "none",
      // Removed the id property as it does not exist on the user type
      expires: session?.expires || "none"
    });

    // 2. Authentication check with better error details
    if (!session?.user?.email) {
      console.error("[Transcription API] Authentication failed: No valid session found");

      // Detailed cookie diagnostic logging
      const cookieList = req.cookies.getAll();
      console.log("[Transcription API] Available cookies:", cookieList.map(c => c.name));
      console.log("[Transcription API] NextAuth session cookie present:",
        cookieList.some(c => c.name.includes('next-auth.session-token')));

      // Return proper 401 with informative message
      return NextResponse.json({
        success: false,
        error: "Authentication required",
        details: "No valid session found. Please sign in and try again.",
        requestId: crypto.randomUUID() // For tracing specific requests in logs
      }, {
        status: 401,
        headers: {
          'WWW-Authenticate': 'Bearer'
        }
      });
    }

    // 3. Process the audio file
    const formData = await req.formData();
    const audioFile = formData.get("audio") as File;

    if (!audioFile) {
      console.error("[Transcription API] Missing audio file in request");
      return NextResponse.json({
        success: false,
        error: "No audio file provided",
        details: "The request must include a file with the name 'audio'"
      }, { status: 400 });
    }

    console.log(`[Transcription API] Processing audio file: ${audioFile.name} (${audioFile.size} bytes) for user: ${session.user.email}`);

    // 4. Perform OpenAI transcription
    const transcription = await openai.audio.transcriptions.create({
      file: audioFile,
      model: "whisper-1",
    });

    console.log("[Transcription API] Transcription successful with length:", transcription.text.length);

    // 5. Return successful response
    return NextResponse.json({
      success: true,
      text: transcription.text,
      user: session.user.email,
      sessionVerified: true
    });

  } catch (error) {
    // 6. Enhanced error handling
    console.error("[Transcription API] Error:", error);

    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    const errorStack = error instanceof Error ? error.stack : null;

    return NextResponse.json({
      success: false,
      error: errorMessage,
      details: errorStack ? errorStack.split('\n')[0] : "No detailed information available",
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// 7. Handle preflight requests
export async function OPTIONS(req: NextRequest): Promise<NextResponse> {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
}