import { useState, useRef, useEffect, useCallback } from "react";
import { Menu, Plus, Loader } from "lucide-react";
import {
  collection,
  addDoc,
  query,
  orderBy,
  limit,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  where,
  serverTimestamp,
} from "firebase/firestore";
import { useSession, signIn } from "next-auth/react";
import { ChatMessages } from "./ChatMessages";
import { EnhancedInputBox } from "./InputBox";
import { ChatHistory } from "./ChatHistory";
import useUpload, { StatusText } from "./useUpload";
import { v4 as uuidv4 } from "uuid";
import { db } from "components/firebase";

// Updated ChatMessage interface with proper ID handling
interface ChatMessage {
  id?: string;          // Firestore document ID
  tempId?: string;      // Temporary tracking ID before Firestore persistence
  role: "user" | "assistant";
  content: string;
  timestamp: string;
  audioUrl?: string;
  fileDocumentId?: string;
}

function ChatTab({ chatId: propChatId, namespace }: { chatId?: string | null; namespace?: string | null }) {
  // Authentication using NextAuth
  const { data: session, status: sessionStatus } = useSession();
  const userId = session?.user?.email || "";
  const isAuthenticated = sessionStatus === "authenticated";
  const isAuthLoading = sessionStatus === "loading";

  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isHistoryVisible, setIsHistoryVisible] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [chatId, setChatId] = useState<string | null>(propChatId || null);
  const [isRecording, setIsRecording] = useState(false);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number | null>(null);
  const [uploadStatusText, setUploadStatusText] = useState<string>("Uploading script...");
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isMounted = useRef(true);
  const [selectedFileNamespace, setSelectedFileNamespace] = useState<string | null>(namespace || null);
  const [fileDocumentId, setFileDocumentId] = useState<string | null>(null);
  const [fileName, setFileName] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isStreaming, setIsStreaming] = useState<boolean>(false);
  const previousChatIdRef = useRef<string | null>(null);
  const messagesProcessingRef = useRef(false);

  const { handleUpload, progress, status, error: uploadError } = useUpload();

  // Enhanced deduplication function that properly handles different ID types
  const deduplicateMessages = (messages: ChatMessage[]): ChatMessage[] => {
    const seen = new Set<string>();
    return messages.filter(message => {
      // Create identifiers with a clear hierarchy of preference
      let signature;

      if (message.id) {
        // Firestore document ID has highest precedence
        signature = `id-${message.id}`;
      } else if (message.tempId) {
        // Temporary ID has secondary precedence
        signature = `temp-${message.tempId}`;
      } else {
        // Fallback to content-based signature if no IDs exist
        signature = `content-${message.role}-${message.content.substring(0, 50)}-${message.timestamp}`;
      }

      if (seen.has(signature)) {
        return false;
      }
      seen.add(signature);
      return true;
    });
  };

  // Session debugging effect
  useEffect(() => {
    if (isAuthenticated && session) {
      fetch('/api/debug-session')
        .then(res => res.json())
        .then(data => {
          if (!data.hasSession && isAuthenticated) {
            console.warn("[ChatTab] ⚠️ Session state mismatch between client and server!");
          }
        })
        .catch(err => console.error("[ChatTab] Session debug check failed:", err));
    }
  }, [session, sessionStatus, isAuthenticated]);

  useEffect(() => {
    if (sessionStatus === "unauthenticated" && !isAuthLoading && error?.includes("Transcription failed")) {
      setError("Please sign in with NextAuth to use voice recording features");
    }
  }, [sessionStatus, isAuthLoading, error]);

  useEffect(() => {
    if (status === StatusText.ERROR && uploadError) {
      setError(`Upload error: ${uploadError}`);
      setIsUploading(false);
      setUploadProgress(null);
    } else if (status === StatusText.UPLOADING) {
      setIsUploading(true);
      setUploadProgress(progress || 0);
      setUploadStatusText("Uploading script...");
    } else if (status === StatusText.PROCESSING) {
      setIsUploading(true);
      setUploadProgress(progress || 0);
      setUploadStatusText("Processing script...");
    } else if (status === StatusText.COMPLETED) {
      setIsUploading(false);
      setUploadProgress(null);
    }
  }, [status, progress, uploadError]);

  useEffect(() => {
    if (namespace) {
      setSelectedFileNamespace(namespace);
      fetchFileDocumentId(namespace);
    }
  }, [namespace]);

  const fetchFileDocumentId = async (namespace: string) => {
    if (!userId) return;
    try {
      const filesRef = collection(db, `users/${userId}/files`);
      const q = query(filesRef, where("namespace", "==", namespace), limit(1));
      const querySnapshot = await getDocs(q);
      if (!querySnapshot.empty) {
        const fileDoc = querySnapshot.docs[0];
        setFileDocumentId(fileDoc.id);

        // Also fetch and set the file name if available
        const fileData = fileDoc.data();
        if (fileData.name) {
          setFileName(fileData.name);
        }
      }
    } catch (err) {
      // Silently handle error
    }
  };

  /**
   * Fetches the most recent file document for the user
   * Used as a fallback when no specific file is selected
   */
  const fetchMostRecentFile = async (): Promise<{id: string, namespace: string, name: string} | null> => {
    if (!userId) return null;

    try {
      const filesRef = collection(db, `users/${userId}/files`);
      const q = query(filesRef, orderBy("createdAt", "desc"), limit(1));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const fileDoc = querySnapshot.docs[0];
        const fileData = fileDoc.data();
        return {
          id: fileDoc.id,
          namespace: fileData.namespace || fileDoc.id,
          name: fileData.name || "Untitled Document"
        };
      }
      return null;
    } catch (err) {
      return null;
    }
  };

  /**
   * Loads messages for a specific chat from Firestore
   * Enhanced with deduplication logic and proper ID management
   */
  const loadMessagesForChat = async (chatId: string) => {
    if (!userId || !chatId) {
      return;
    }

    // Prevent concurrent loading operations
    if (messagesProcessingRef.current) {
      return;
    }

    messagesProcessingRef.current = true;
    setIsLoading(true);
    setError(null);

    try {
      // Query messages for the selected chat, ordered by creation time
      const messagesRef = collection(db, `users/${userId}/chats/${chatId}/messages`);
      const messagesQuery = query(messagesRef, orderBy("createdAt", "asc"));
      const messagesSnapshot = await getDocs(messagesQuery);

      // Transform message data to match the expected interface
      const messages: ChatMessage[] = messagesSnapshot.docs.map((doc) => {
        const data = doc.data();
        return {
          id: doc.id,  // Use Firestore document ID
          tempId: data.tempId, // Include tempId if present for correlation
          role: data.role,
          content: data.text || "", // Map 'text' field to 'content' expected by UI
          timestamp: data.createdAt?.toDate?.()?.toISOString() || new Date().toISOString(),
          audioUrl: data.audioUrl,
          fileDocumentId: data.fileDocumentId || fileDocumentId,
        };
      });

      // Replace current messages with deduplicated loaded messages
      // Using fresh state instead of merging to avoid duplication
      setChatMessages(deduplicateMessages(messages));
    } catch (err) {
      console.error("Error loading messages:", err);
      setError(`Failed to load messages: ${err instanceof Error ? err.message : "Unknown error"}`);
    } finally {
      setIsLoading(false);
      messagesProcessingRef.current = false;
    }
  };

  useEffect(() => {
    const loadChat = async () => {
      if (!userId) {
        setError("Please sign in to access chats.");
        return;
      }
      try {
        if (propChatId) {
          setChatId(propChatId);
          await loadMessagesForChat(propChatId);
          await fetchChatFileNamespace(propChatId);
          return;
        }
        if (selectedFileNamespace) {
          const chatsRef = collection(db, `users/${userId}/chats`);
          const chatsQuery = query(
            chatsRef,
            where("fileNamespace", "==", selectedFileNamespace),
            orderBy("createdAt", "desc"),
            limit(1)
          );
          const chatsSnapshot = await getDocs(chatsQuery);
          if (!chatsSnapshot.empty) {
            const recentChat = chatsSnapshot.docs[0];
            const recentChatId = recentChat.id;
            setChatId(recentChatId);
            await loadMessagesForChat(recentChatId);
            const chatData = recentChat.data();
            if (chatData.fileDocumentId) {
              setFileDocumentId(chatData.fileDocumentId);

              // If we have a fileDocumentId, fetch the file name
              if (userId) {
                try {
                  const fileRef = doc(db, `users/${userId}/files/${chatData.fileDocumentId}`);
                  const fileSnapshot = await getDoc(fileRef);
                  if (fileSnapshot.exists()) {
                    const fileData = fileSnapshot.data();
                    if (fileData.name) {
                      setFileName(fileData.name);
                    }
                  }
                } catch (error) {
                  // Silently handle error
                }
              }
            } else if (chatData.fileNamespace) {
              await fetchFileDocumentId(chatData.fileNamespace);
            }
          } else {
            await createNewChat();
          }
        } else {
          const chatsRef = collection(db, `users/${userId}/chats`);
          const chatsQuery = query(chatsRef, orderBy("createdAt", "desc"), limit(1));
          const chatsSnapshot = await getDocs(chatsQuery);
          if (!chatsSnapshot.empty) {
            const recentChat = chatsSnapshot.docs[0];
            const recentChatId = recentChat.id;
            setChatId(recentChatId);
            await loadMessagesForChat(recentChatId);
            await fetchChatFileNamespace(recentChatId);
          } else {
            await createNewChat();
          }
        }
      } catch (err) {
        if (isMounted.current) {
          setError(
            "Failed to load chat: " +
              (err instanceof Error ? err.message : "Unknown error")
          );
        }
      }
    };

    if (isAuthenticated) {
      loadChat();
    }
  }, [userId, propChatId, selectedFileNamespace, isAuthenticated]);

  // Only load messages when chat ID changes and not already processing
  useEffect(() => {
    if (chatId &&
        chatId !== previousChatIdRef.current &&
        userId &&
        !messagesProcessingRef.current) {
      loadMessagesForChat(chatId);
      previousChatIdRef.current = chatId;
    }
  }, [chatId, userId]);

  const fetchChatFileNamespace = async (chatId: string) => {
    try {
      if (!userId) return;
      const chatRef = doc(db, `users/${userId}/chats/${chatId}`);
      const chatSnapshot = await getDoc(chatRef);
      if (chatSnapshot.exists()) {
        const chatData = chatSnapshot.data();
        if (chatData.fileNamespace) {
          setSelectedFileNamespace(chatData.fileNamespace);
        }
        if (chatData.fileDocumentId) {
          setFileDocumentId(chatData.fileDocumentId);

          // If we have a fileDocumentId, fetch the file name
          try {
            const fileRef = doc(db, `users/${userId}/files/${chatData.fileDocumentId}`);
            const fileSnapshot = await getDoc(fileRef);
            if (fileSnapshot.exists()) {
              const fileData = fileSnapshot.data();
              if (fileData.name) {
                setFileName(fileData.name);
              }
            }
          } catch (error) {
            // Silently handle error
          }
        } else if (chatData.fileNamespace) {
          await fetchFileDocumentId(chatData.fileNamespace);
        }
      }
    } catch (err) {
      // Silently handle error
    }
  };

  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  /**
   * Creates a new chat with proper namespace alignment
   * @param fileNamespace - Optional namespace of the file
   * @param fileDocId - Optional document ID of the file
   * @returns The ID of the newly created chat, or null if creation failed
   */
  const createNewChat = async (fileNamespace?: string, fileDocId?: string) => {
    if (!userId) {
      setError("Please sign in to create a new chat.");
      return null;
    }

    try {
      let firstMessageText = "New Rehearsal";
      const chatsRef = collection(db, `users/${userId}/chats`);

      // Proper namespace and fileDocumentId handling
      let actualFileDocId: string;
      let actualNamespace: string;

      // Case 1: Both fileNamespace and fileDocId provided - use as is
      if (fileNamespace && fileDocId) {
        actualNamespace = fileNamespace;
        actualFileDocId = fileDocId;
      }
      // Case 2: Only fileDocId provided - use as namespace too
      else if (fileDocId) {
        actualNamespace = fileDocId;
        actualFileDocId = fileDocId;
      }
      // Case 3: Only fileNamespace provided - use as fileDocId too
      else if (fileNamespace) {
        actualNamespace = fileNamespace;
        actualFileDocId = fileNamespace;
      }
      // Case 4: Neither provided - fetch most recent file or create an error
      else {
        // Try to get the most recent file
        const recentFile = await fetchMostRecentFile();

        if (recentFile) {
          // Use the recent file's details
          actualNamespace = recentFile.namespace;
          actualFileDocId = recentFile.id;
          setFileName(recentFile.name);
        } else {
          // No recent file found - show error to user
          setError("Please upload or select a file before creating a chat.");
          return null;
        }
      }

      // Initialize with required fields - ensuring namespace and fileDocumentId consistency
      const chatData = {
        createdAt: serverTimestamp(),
        userId: userId,
        firstMessage: firstMessageText,
        lastUpdated: serverTimestamp(),
        fileNamespace: actualNamespace,
        fileDocumentId: actualFileDocId
      };

      const docRef = await addDoc(chatsRef, chatData);
      const newChatId = docRef.id;
      setChatId(newChatId);
      setChatMessages([]);

      // Update state with new values
      setSelectedFileNamespace(actualNamespace);
      setFileDocumentId(actualFileDocId);

      return newChatId;
    } catch (err) {
      if (isMounted.current) {
        setError(
          "Failed to create new chat: " +
            (err instanceof Error ? err.message : "Unknown error")
        );
      }
      return null;
    }
  };

  /**
   * Handler for chat selection from ChatHistory
   * Loads the selected chat and its messages
   * @param selectedChatId - The ID of the selected chat
   */
  const handleChatSelect = async (selectedChatId: string) => {
    if (!selectedChatId || !userId) {
      return;
    }

    setChatId(selectedChatId);
    setIsLoading(true);
    setError(null);

    try {
      // First fetch the chat details to get file information
      const chatRef = doc(db, `users/${userId}/chats/${selectedChatId}`);
      const chatSnapshot = await getDoc(chatRef);

      if (chatSnapshot.exists()) {
        const chatData = chatSnapshot.data();

        // Update file context information
        if (chatData.fileNamespace) {
          setSelectedFileNamespace(chatData.fileNamespace);
        }

        if (chatData.fileDocumentId) {
          setFileDocumentId(chatData.fileDocumentId);

          // Fetch file details to get the name
          const fileRef = doc(db, `users/${userId}/files/${chatData.fileDocumentId}`);
          const fileSnapshot = await getDoc(fileRef);

          if (fileSnapshot.exists()) {
            const fileData = fileSnapshot.data();
            setFileName(fileData.name || null);
          }
        }
      }

      // Load messages for the selected chat
      await loadMessagesForChat(selectedChatId);
    } catch (error) {
      console.error("Error selecting chat:", error);
      setError("Failed to load the selected chat");
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Sends a message and processes the AI response
   * Fully revised to handle message ID correlation properly
   */
  const sendMessage = useCallback(
    async (messageText: string) => {
      if (!messageText.trim() || !chatId || isLoading || !userId) return;

      // Generate a temporary ID for tracking the message before Firestore ID is available
      const tempUserMessageId = `temp-${uuidv4()}`;

      const userMessage: ChatMessage = {
        role: "user",
        content: messageText,
        timestamp: new Date().toISOString(),
        tempId: tempUserMessageId
      };

      // Add the message to UI state immediately for responsive feedback
      setChatMessages(prevMessages => deduplicateMessages([...prevMessages, userMessage]));
      setIsLoading(true);
      setError(null);

      try {
        // Resolve the current file document ID
        let currentFileDocId = fileDocumentId;
        if (!currentFileDocId && selectedFileNamespace) {
          await fetchFileDocumentId(selectedFileNamespace);
          currentFileDocId = fileDocumentId;
        }
        if (!currentFileDocId) {
          const chatDoc = await getDoc(doc(db, `users/${userId}/chats/${chatId}`));
          if (chatDoc.exists()) {
            const chatData = chatDoc.data();
            if (chatData.fileDocumentId) {
              currentFileDocId = chatData.fileDocumentId;
              setFileDocumentId(currentFileDocId);
            } else if (chatData.fileNamespace && !selectedFileNamespace) {
              setSelectedFileNamespace(chatData.fileNamespace);
              await fetchFileDocumentId(chatData.fileNamespace);
              currentFileDocId = fileDocumentId;
            }
          }
        }

        // Store user message in Firestore first to get its document ID
        const userMessageRef = await addDoc(
          collection(db, `users/${userId}/chats/${chatId}/messages`),
          {
            role: "user",
            text: messageText,
            createdAt: serverTimestamp(),
            userId: userId,
            fileDocumentId: currentFileDocId,
            tempId: tempUserMessageId // Store the temp ID for correlation
          }
        );

        // Update message in UI with real Firestore ID
        setChatMessages(prevMessages => {
          return prevMessages.map(msg => {
            if (msg.tempId === tempUserMessageId) {
              return {
                ...msg,
                id: userMessageRef.id,
                tempId: undefined // Clear tempId now that we have a real ID
              };
            }
            return msg;
          });
        });

        // Process message through API
        const response = await fetch("/api/processMessage", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            userId,
            chatId,
            messageText,
            fileDocumentId: currentFileDocId,
          }),
        });

        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        const data = await response.json();
        if (data.success) {
          // Generate temp ID for assistant message
          const tempAssistantMessageId = `temp-${uuidv4()}`;

          const assistantMessage: ChatMessage = {
            role: "assistant",
            content: data.content,
            timestamp: new Date().toISOString(),
            audioUrl: data.audioUrl,
            tempId: tempAssistantMessageId
          };

          // Add assistant message to UI
          setChatMessages(prevMessages => deduplicateMessages([...prevMessages, assistantMessage]));
          setIsLoading(false);

          // Update chat metadata
          if (chatMessages.length === 0) {
            await updateDoc(doc(db, `users/${userId}/chats/${chatId}`), {
              firstMessage: messageText,
              lastUpdated: serverTimestamp(),
            });
          } else {
            await updateDoc(doc(db, `users/${userId}/chats/${chatId}`), {
              lastUpdated: serverTimestamp(),
            });
          }

          // Store assistant message in Firestore
          const assistantMessageRef = await addDoc(
            collection(db, `users/${userId}/chats/${chatId}/messages`),
            {
              role: "assistant",
              text: data.content,
              createdAt: serverTimestamp(),
              audioUrl: data.audioUrl,
              fileDocumentId: currentFileDocId,
              tempId: tempAssistantMessageId
            }
          );

          // Update assistant message in UI with real Firestore ID
          setChatMessages(prevMessages => {
            return prevMessages.map(msg => {
              if (msg.tempId === tempAssistantMessageId) {
                return {
                  ...msg,
                  id: assistantMessageRef.id,
                  tempId: undefined // Clear tempId now that we have a real ID
                };
              }
              return msg;
            });
          });

          // Do NOT call loadMessagesForChat as we've already updated the UI state
        } else {
          throw new Error(data.error || "Unknown error from API");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
        setIsLoading(false);
      }
    },
    [chatId, userId, selectedFileNamespace, fileDocumentId, chatMessages.length, isLoading]
  );

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);
      mediaRecorderRef.current = recorder;
      const chunks: Blob[] = [];
      recorder.ondataavailable = (e) => chunks.push(e.data);
      recorder.onstop = () => {
        const blob = new Blob(chunks, { type: "audio/mp3" });
        processAudioBlob(blob);
        stream.getTracks().forEach((track) => track.stop());
      };
      recorder.start();
      setIsRecording(true);
    } catch (error) {
      setError("Failed to start recording. Please check your microphone permissions.");
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  const processAudioBlob = async (blob: Blob) => {
    setError(null);

    if (sessionStatus === "unauthenticated") {
      setError("You must be signed in with NextAuth to use voice recording");

      if (confirm("Sign in with NextAuth to use voice recording?")) {
        signIn('google', { callbackUrl: window.location.href });
      }
      return;
    }

    setIsLoading(true);

    const formData = new FormData();
    formData.append("audio", blob, `voice-${Date.now()}.mp3`);

    try {
      const response = await fetch("/api/transcribeAudio", {
        method: "POST",
        credentials: 'include',
        body: formData,
      });

      if (response.status === 401) {
        console.error("[ChatTab] Authentication failed in API call despite client session");
        throw new Error("Session not recognized by server. Please sign in again.");
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("[ChatTab] Transcription API error:", errorData);
        throw new Error(errorData.error || `Server error: ${response.status}`);
      }

      const data = await response.json();
      if (data.success) {
        sendMessage(data.text);
      } else {
        throw new Error(data.error || "Transcription failed with no specific error");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown transcription error";
      console.error("[ChatTab] Audio processing error:", errorMessage);

      if (errorMessage.includes("Authentication") ||
          errorMessage.includes("sign in") ||
          errorMessage.includes("session")) {
        setError("Session authentication issue. Please sign in again.");

        setTimeout(() => {
          if (confirm("Your session appears to be invalid. Sign in again?")) {
            signIn('google', { callbackUrl: window.location.href });
          }
        }, 500);
      } else {
        setError(`Transcription failed: ${errorMessage}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleMicClick = () => {
    if (sessionStatus === "unauthenticated") {
      setError("Please sign in with NextAuth to use voice recording");
      if (confirm("Sign in with NextAuth to use voice recording?")) {
        signIn('google', { callbackUrl: window.location.href });
      }
      return;
    }

    if (!userId) {
      setError("Please sign in to use voice recording");
      return;
    }

    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  /**
   * Handles file upload and creates a new chat with proper ID management
   */
  const handleFileUpload = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file || !userId) {
        setError("No file selected or user not authenticated.");
        return;
      }
      try {
        // Generate document ID for the file
        const docId = uuidv4();

        // Upload file to storage and process it
        await handleUpload(file, null, userId, docId);

        // Create a new chat with proper namespace/fileDocId relationship
        // Pass docId as both namespace and fileDocId to ensure consistency
        const newChatId = await createNewChat(docId, docId);

        if (newChatId) {
          setSelectedFileNamespace(docId);
          setFileDocumentId(docId);
          setFileName(file.name); // Set the file name when uploading
          setChatId(newChatId);

          // First add welcome message to Firestore to get a real document ID
          const messagesRef = collection(db, `users/${userId}/chats/${newChatId}/messages`);
          const welcomeMessageData = {
            role: "assistant",
            text: "File processed successfully! How can I assist with your script?",
            createdAt: serverTimestamp(),
            fileDocumentId: docId
          };

          // Add to Firestore first to get a document ID
          const welcomeDocRef = await addDoc(messagesRef, welcomeMessageData);

          // Create message object with the Firestore ID for client state
          const initialMessage: ChatMessage = {
            id: welcomeDocRef.id, // Use the real Firestore document ID
            role: "assistant",
            content: welcomeMessageData.text,
            timestamp: new Date().toISOString(),
            fileDocumentId: docId
          };

          // Set state with the message containing the real Firestore ID
          setChatMessages([initialMessage]);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Upload failed");
      }
    },
    [userId, handleUpload, createNewChat]
  );

  const handleAttachFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Format display information for the header
  const getHeaderDisplayText = () => {
    if (selectedFileNamespace) {
      // Show file name if available, otherwise show namespace
      const fileDisplay = fileName ? fileName : `Script: ${selectedFileNamespace.substring(0, 8)}...`;
      // Always show chat ID if available
      const chatDisplay = chatId ? `Chat ID: ${chatId.substring(0, 8)}...` : "";

      // Return combined information
      return chatDisplay ? `${fileDisplay} | ${chatDisplay}` : fileDisplay;
    } else if (chatId) {
      return `Chat ID: ${chatId.substring(0, 8)}...`;
    } else {
      return "No chat selected";
    }
  };

  return (
    <div className="flex h-full relative courier-font">
      {sessionStatus === "unauthenticated" && (
        <div className="absolute top-0 right-0 bg-amber-600 text-white px-3 py-1 text-xs rounded-bl-md z-10 flex items-center">
          <span>Voice recording requires NextAuth</span>
          <button
            onClick={() => signIn('google', { callbackUrl: window.location.href })}
            className="ml-2 bg-white text-amber-600 px-2 py-0.5 rounded text-xs font-medium"
          >
            Sign in
          </button>
        </div>
      )}

      <ChatHistory
        isVisible={isHistoryVisible}
        onClose={() => setIsHistoryVisible(false)}
        currentChatId={chatId}
        onChatSelect={handleChatSelect}
        userId={userId}
        namespace={selectedFileNamespace}
      />
      <div className="w-full flex flex-col h-full">
        <div className="p-4 flex items-center justify-between border-b border-gray-200 dark:border-white/10 transition-colors duration-300">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setIsHistoryVisible(!isHistoryVisible)}
              className="p-2 rounded-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              <Menu className="w-5 h-5 text-gray-700 dark:text-white" />
            </button>
            <button
              onClick={() => createNewChat()}
              className="flex items-center space-x-1 px-3 py-2 rounded-md bg-blue-50 dark:bg-gray-700/40 text-blue-700 hover:text-blue-900 hover:font-semibold dark:text-white border border-blue-200 dark:border-transparent hover:border-transparent"
            >
              <Plus className="w-4 h-4" />
              <span className="text-sm">New Chat</span>
            </button>
          </div>
          <span className="text-gray-400 text-sm truncate max-w-[200px]">
            {getHeaderDisplayText()}
          </span>
        </div>

        {error && (
          <div className="text-red-500 text-center p-4">
            {error}
            <button
              onClick={() => setError(null)}
              className="ml-2 text-purple-500 underline"
            >
              Dismiss
            </button>
          </div>
        )}

        {chatMessages.length === 0 && !error && !isUploading && !isLoading && (
          <div className="flex-1 flex flex-col items-center justify-center p-4 text-gray-400 text-sm">
            <p className="text-xl font-bold">Start a conversation with your Castmate Tutor</p>
            <p className="mt-2">Select a script from the sidebar then speak or chat with your tutor:</p>
          </div>
        )}

        {isUploading && (
          <div className="flex-1 flex flex-col items-center justify-center p-4 text-gray-400 text-sm">
            <div className="flex flex-col items-center">
              <Loader className="w-8 h-8 text-purple-500 animate-spin mb-4" />
              <p className="text-gray-300 mb-2">{uploadStatusText}</p>
              {uploadProgress !== null && (
                <div className="w-64 bg-gray-700 rounded-full h-2.5 mb-4">
                  <div
                    className="bg-purple-500 h-2.5 rounded-full transition-all duration-300"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              )}
              <p className="text-xs text-gray-500">
                Please wait while we process your file
              </p>
            </div>
          </div>
        )}

        {isLoading && chatMessages.length === 0 && !error && !isUploading && (
          <div className="flex-1 flex items-center justify-center">
            <div className="flex flex-col items-center">
              <div className="flex space-x-2 mb-3">
                <div className="w-3 h-3 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: "0ms" }}></div>
                <div className="w-3 h-3 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: "150ms" }}></div>
                <div className="w-3 h-3 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: "300ms" }}></div>
              </div>
              <p className="text-gray-400 text-sm">Loading messages...</p>
            </div>
          </div>
        )}

        {(chatMessages.length > 0 || (error && !isUploading)) && (
          <ChatMessages
            chatMessages={chatMessages}
            error={error}
            isLoading={isLoading}
            isStreaming={isStreaming}
            messagesEndRef={messagesEndRef}
          />
        )}

        <div className="relative">
          <EnhancedInputBox
            onSendMessage={sendMessage}
            isLoading={isLoading || isUploading}
            onAttachFile={handleAttachFile}
            onMicClick={handleMicClick}
            isRecording={isRecording}
            placeholder={chatId ? "Ask your Scene Mate..." : "Chat ID required"}
            disabled={!chatId}
          />
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            onChange={handleFileUpload}
            accept=".pdf,.docx,.doc,.txt,.jpg,.jpeg,.png"
            disabled={isLoading || isUploading}
          />
        </div>
      </div>
    </div>
  );
}

export default ChatTab