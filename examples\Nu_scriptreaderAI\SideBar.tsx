"use client"

import type React from "react"
import { useState } from "react"
import { File, ChevronRight, Upload, Loader, Search, FolderOpen, X, AlertCircle, Menu } from "lucide-react"
import type { Session } from "next-auth"

interface ScriptFile {
  id: string
  name: string
  namespace: string
}

interface SideBarProps {
  activeTab: string | null
  setActiveTab: (id: string) => void | Promise<void> // Updated to support async script selection
  scriptFiles: ScriptFile[]
  loading: boolean
  error: string | null
  setError: (error: string | null) => void
  isUploading: boolean
  uploadProgress: number | null
  uploadStatusText: string
  handleUploadClick: () => void
  handleFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void // Updated signature
  fileInputRef: React.RefObject<HTMLInputElement>
  sessionStatus: "loading" | "authenticated" | "unauthenticated"
  session: Session | null
  onClose: () => void
  isMobileSidebarOpen: boolean
  setIsMobileSidebarOpen: (isOpen: boolean) => void
}

const SideBar = ({
  activeTab,
  setActiveTab,
  scriptFiles,
  loading,
  error,
  setError,
  isUploading,
  uploadProgress,
  uploadStatusText,
  handleUploadClick,
  handleFileUpload,
  fileInputRef,
  sessionStatus,
  session,
  onClose,
  isMobileSidebarOpen,
  setIsMobileSidebarOpen,
}: SideBarProps) => {
  const [searchTerm, setSearchTerm] = useState("")
  const filteredScripts = scriptFiles.filter((file) => file.name.toLowerCase().includes(searchTerm.toLowerCase()))

  return (
    <div className="md:w-64 bg-gray-50 dark:bg-gray-900 backdrop-blur-sm border-b md:border-b-0 md:border-r border-gray-200 dark:border-gray-700 flex flex-col h-full overflow-hidden relative transition-colors duration-300 courier-font">
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 transition-colors duration-300">
        <div className="flex items-center">
          <button
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              setIsMobileSidebarOpen(!isMobileSidebarOpen);
            }}
            className="md:hidden mr-2 p-1.5 rounded-full bg-green-600/20 hover:bg-green-600/30 text-green-400 hover:text-green-300 transition-colors"
            aria-label="Hide menu"
            type="button"
          >
            <Menu className="w-3.5 h-3.5" />
          </button>
          <h2 className="text-base font-medium text-gray-700 dark:text-green-400 flex items-center transition-colors duration-300">
            <FolderOpen className="w-4 h-4 mr-2" />
            <span>Scripts</span>
          </h2>
        </div>
        <div className="flex items-center">
          <button
            onClick={handleUploadClick}
            className="p-1.5 rounded-full bg-blue-500 dark:bg-gray-600/80 hover:bg-blue-600 dark:hover:bg-gray-500 text-white transition-all duration-200 shadow-sm mr-2"
            title="Upload Script"
            disabled={isUploading || !session?.user?.email}
          >
            {isUploading ? <Loader className="w-3.5 h-3.5 animate-spin" /> : <Upload className="w-3.5 h-3.5" />}
          </button>
          <button
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              onClose()
            }}
            className="md:hidden p-1.5 rounded-full bg-gray-600/80 hover:bg-gray-500 text-white transition-all duration-200 shadow-lg shadow-gray-500/20"
            aria-label="Close"
            type="button"
          >
            <X className="w-3.5 h-3.5" />
          </button>
        </div>
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          onChange={handleFileUpload} // Updated to use handleFileUpload directly
          accept=".pdf,.docx,.doc,.txt,.jpg,.jpeg,.png"
          disabled={isUploading || !session?.user?.email}
        />
      </div>

      <div className="px-3 pt-3 pb-2">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 w-3.5 h-3.5 text-gray-400 dark:text-gray-500" />
          <input
            type="text"
            placeholder="Search scripts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-white dark:bg-black/60 border border-gray-200 dark:border-white/10 rounded-md pl-8 pr-3 py-2 text-xs text-gray-700 dark:text-gray-300 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:focus:ring-gray-500/50 transition-colors duration-300"
          />
          {searchTerm && (
            <button
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                setSearchTerm("")
              }}
              className="absolute right-2.5 top-2.5"
              type="button"
            >
              <X className="w-3.5 h-3.5 text-gray-500 hover:text-gray-300" />
            </button>
          )}
        </div>
      </div>

      {error && (
        <div className="mx-3 mb-2 text-xs bg-red-500/10 rounded-md overflow-hidden">
          <div className="flex items-start p-2">
            <AlertCircle className="w-3.5 h-3.5 text-red-400 mt-0.5 mr-2 flex-shrink-0" />
            <div className="flex-1 text-red-400">{error}</div>
            <button onClick={() => setError(null)} className="ml-2 text-gray-400 hover:text-gray-300 text-xs">
              Dismiss
            </button>
          </div>
        </div>
      )}

      {isUploading && (
        <div className="mx-3 mb-2 bg-black/60 rounded-md p-2.5">
          <div className="flex flex-col">
            <p className="text-xs text-gray-300 mb-1.5">{uploadStatusText}</p>
            {uploadProgress !== null && (
              <div className="w-full bg-gray-800 rounded-full h-1">
                <div
                  className="bg-gray-500 h-1 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            )}
            {uploadStatusText.includes("Formatting") && (
              <p className="text-xs text-gray-300 mt-1">
                ✨ Preparing your script for instant access
              </p>
            )}
          </div>
        </div>
      )}

      <div className="md:hidden px-3 pb-3">
        <select
          value={activeTab || ""}
          onChange={(e) => {
            const value = e.target.value;
            if (value) {
              setActiveTab(value);
            }
          }}
          className="w-full px-3 py-2 rounded-md bg-black/60 text-xs text-white border border-white/10 focus:outline-none focus:ring-1 focus:ring-gray-500/50"
          disabled={!session?.user?.email || loading || scriptFiles.length === 0}
        >
          <option value="" disabled>
            {loading ? "Loading scripts..." : scriptFiles.length === 0 ? "No scripts available" : "Select a script"}
          </option>
          {filteredScripts.map((file) => (
            <option key={file.id} value={file.id} className="text-white bg-black">
              {file.name}
            </option>
          ))}
        </select>
      </div>

      <div className={`hidden md:block flex-1 overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-track-transparent scrollbar-thumb-white/10 hover:scrollbar-thumb-gray-500/30 ${isMobileSidebarOpen ? 'block' : 'hidden md:block'}`}>
        {sessionStatus === "loading" ? (
          <div className="flex items-center justify-center h-20 text-xs text-gray-400">
            <Loader className="w-3.5 h-3.5 animate-spin mr-2" />
            <span>Loading session...</span>
          </div>
        ) : !session?.user?.email ? (
          <div className="flex flex-col items-center justify-center h-20 px-4 text-center">
            <p className="text-xs text-gray-400">Please sign in to view scripts</p>
          </div>
        ) : loading ? (
          <div className="flex items-center justify-center h-20 text-xs text-gray-400">
            <Loader className="w-3.5 h-3.5 animate-spin mr-2" />
            <span>Loading scripts...</span>
          </div>
        ) : filteredScripts.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-20 px-4 text-center">
            <p className="text-xs text-gray-400">{searchTerm ? "No matching scripts found" : "No scripts found"}</p>
          </div>
        ) : (
          <div className="pt-1 pb-3">
            {filteredScripts.map((file) => {
              const isActive = activeTab === file.id

              return (
                <button
                  key={file.id}
                  onClick={() => setActiveTab(file.id)}
                  className={`w-[calc(100%-16px)] px-2 py-1.5 mx-2 my-1 rounded-md flex items-center text-left transition-all duration-200 group ${
                    isActive
                      ? "bg-blue-50 dark:bg-gray-700/40 text-blue-700 dark:text-white border border-blue-200 dark:border-transparent"
                      : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-white/5 hover:text-gray-800 dark:hover:text-white"
                  }`}
                >
                  <div
                    className={`flex items-center justify-center w-5 h-5 rounded ${
                      isActive
                        ? "bg-blue-100 dark:bg-black/60"
                        : "bg-gray-100 dark:bg-black/60"
                    } mr-2 transition-colors duration-200`}
                  >
                    <File className={`w-3 h-3 ${
                      isActive
                        ? "text-blue-600 dark:text-green-500"
                        : "text-gray-500 dark:text-green-500"
                    } transition-colors duration-200`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-baseline">
                      <span className="font-medium text-sm truncate">{file.name}</span>
                    </div>
                  </div>
                  <ChevronRight
                    className={`w-4 h-4 transition-transform duration-200 opacity-40 ${
                      isActive ? "rotate-90 text-white opacity-100" : "text-gray-400 group-hover:translate-x-0.5 group-hover:opacity-80"
                    }`}
                  />
                </button>
              )
            })}
          </div>
        )}
      </div>

      <div className="hidden md:flex items-center justify-between px-4 py-2 border-t border-gray-200 dark:border-white/5 bg-gray-100 dark:bg-black/60 text-[10px] text-gray-500 transition-colors duration-300">
        <span>
          {filteredScripts.length} script{filteredScripts.length !== 1 ? "s" : ""}
        </span>
        {searchTerm && (
          <span>
            {filteredScripts.length} of {scriptFiles.length} shown
          </span>
        )}
      </div>
    </div>
  )
}

export default SideBar