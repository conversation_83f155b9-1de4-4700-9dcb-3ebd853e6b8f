"use client"

import { motion } from "framer-motion"

interface FileDetailsProps {
  activeTab: string | null
  fileName: string | null
  namespace: string | null
  apiConfigStatus: 'unchecked' | 'valid' | 'invalid' | 'connecting'
  sessionStatus: 'loading' | 'authenticated' | 'unauthenticated'
  detailedErrorInfo?: string | null
}

const FileDetails = ({
  activeTab,
  fileName,
  namespace,
  apiConfigStatus,
  sessionStatus,
  detailedErrorInfo
}: FileDetailsProps) => {
  if (!activeTab) return null

  return (
    <div className="space-y-6 courier-font">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-blue-50 dark:bg-black/20 backdrop-blur-sm rounded-lg p-3 sm:p-4 border border-blue-200 dark:border-white/5 hover:border-blue-300 dark:hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/10 dark:hover:shadow-purple-500/10"
      >
        <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">Script Details</h3>
        <div className="grid grid-cols-1 gap-4 text-xs sm:text-sm">
          <div className="text-gray-600 dark:text-gray-400">
            <span className="font-medium text-blue-600 dark:text-purple-400">File name:</span> {fileName || "No file selected"}
          </div>
          <div className="text-gray-600 dark:text-gray-400">
            <span className="font-medium text-blue-600 dark:text-purple-400">Namespace:</span> {namespace || "N/A"}
          </div>
          <div className="text-gray-600 dark:text-gray-400">
            <span className="font-medium text-blue-600 dark:text-purple-400">Document ID:</span> {activeTab || "N/A"}
          </div>

          {/* ElevenLabs environment variable debugging section */}
          <div className="mt-4 border-t border-gray-200 dark:border-white/10 pt-4 transition-colors duration-300">
            <h4 className="text-sm font-medium text-blue-600 dark:text-purple-400 mb-2">API Configuration Status</h4>
            <div className="space-y-2">
              <div className="flex items-center">
                <div className="w-4 h-4 mr-2">
                  {process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************'? (
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  ) : (
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  )}
                </div>
                <span className="text-xs">
                  API Key: {process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************' ? "Configured" : "Missing"}
                </span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 mr-2">
                  {process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq' ? (
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  ) : (
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  )}
                </div>
                <span className="text-xs">
                  Agent ID: {process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq' ? "Configured" : "Missing (using fallback)"}
                </span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 mr-2">
                  <div className={`w-3 h-3 rounded-full ${
                    apiConfigStatus === 'valid'
                      ? "bg-green-500"
                      : apiConfigStatus === 'invalid'
                        ? "bg-red-500"
                        : apiConfigStatus === 'connecting'
                          ? "bg-yellow-500"
                          : "bg-gray-500"
                  }`}></div>
                </div>
                <span className="text-xs">
                  Connection Status: {
                    apiConfigStatus === 'valid'
                      ? "Connected"
                      : apiConfigStatus === 'invalid'
                        ? "Failed"
                        : apiConfigStatus === 'connecting'
                          ? "Connecting..."
                          : "Not Verified"
                  }
                </span>
              </div>
              <div className="flex items-center">
                <div className="w-4 h-4 mr-2">
                  <div className={`w-3 h-3 rounded-full ${
                    sessionStatus === 'authenticated'
                      ? "bg-green-500"
                      : sessionStatus === 'loading'
                        ? "bg-yellow-500"
                        : "bg-red-500"
                  }`}></div>
                </div>
                <span className="text-xs">
                  Authentication: {
                    sessionStatus === 'authenticated'
                      ? "Signed In"
                      : sessionStatus === 'loading'
                        ? "Loading..."
                        : "Not Signed In"
                  }
                </span>
              </div>
            </div>
          </div>

          {/* Display detailed error information if available */}
          {detailedErrorInfo && (
            <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-500/30 text-xs transition-colors duration-300">
              <div className="font-medium text-red-600 dark:text-red-400 mb-1">Error Details:</div>
              <div className="text-red-700 dark:text-red-300 overflow-x-auto">{detailedErrorInfo}</div>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  )
}

export default FileDetails